<?php

namespace App\Filters;

use Illuminate\Database\Eloquent\Builder;
use Spatie\QueryBuilder\Filters\Filter;

class HasShippingFeeFilter implements Filter
{
    public function __invoke(Builder $query, $value, string $property)
    {
        if (filter_var($value, FILTER_VALIDATE_BOOLEAN)) {
            $query->whereExists(function ($subQuery) {
                $subQuery->selectRaw(1)
                    ->from('shipping_fees')
                    ->whereColumn('shipping_fees.country', 'countries.iso_code');
            });
        }
        return $query;
    }
} 