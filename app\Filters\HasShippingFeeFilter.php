<?php

namespace App\Filters;

use Illuminate\Database\Eloquent\Builder;
use Spatie\QueryBuilder\Filters\Filter;

class HasShippingFeeFilter implements Filter
{
    public function __invoke(Builder $query, $value, string $property)
    {
        // 添加调试日志
        logger()->info('HasShippingFeeFilter 被调用', [
            'value' => $value,
            'property' => $property,
            'value_type' => gettype($value),
            'boolean_validation' => filter_var($value, FILTER_VALIDATE_BOOLEAN)
        ]);

        if (filter_var($value, FILTER_VALIDATE_BOOLEAN)) {
            logger()->info('HasShippingFeeFilter 应用过滤条件');

            $query->whereExists(function ($subQuery) {
                $subQuery->selectRaw(1)
                    ->from('shipping_fees')
                    ->whereColumn('shipping_fees.country', 'countries.iso_code');
            });

            // 记录生成的SQL
            logger()->info('HasShippingFeeFilter SQL', [
                'sql' => $query->toSql(),
                'bindings' => $query->getBindings()
            ]);
        } else {
            logger()->info('HasShippingFeeFilter 跳过过滤条件');
        }

        return $query;
    }
} 