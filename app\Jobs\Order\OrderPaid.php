<?php

namespace App\Jobs\Order;

use App\Constants\QueueKey;
use App\Models\Enums\Order\OrderPaidStatusEnum;
use App\Models\Enums\Order\OrderStatusEnum;
use App\Models\Order\Order;
use App\Models\Order\Payment\Payment;
use App\Models\Order\Payment\PaypalOrder;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class OrderPaid implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 订单支付成功操作
     */
    public function __construct(public Order $order, public Payment $payment) {}

    /**
     * 处理支付结果
     */
    public function handle(): void
    {
        try {
            // 处理支付结果
            $this->payment->loadPaymentInfo();
            $paidStatus = $this->payment->getPaidStatus();
            // 支付成功
            if ($paidStatus == OrderPaidStatusEnum::Paid) {
                // 支付成功
                $this->order->payment()->associate($this->payment)->save();
            } else {
                // 未成功则失败
                $this->order->payment()->update([
                    'status' => OrderStatusEnum::PaidFail
                ]);
                return;
            }

            $this->order->update([
                'paid_amount' => $this->payment->getPaidAmount(),
                'paid_at' => $this->payment->getPaidAt(),
                'paid_type' => $this->payment->getPaidType(),
                'paid_status' => $paidStatus,
                'status' => $this->payment->getPaidStatus() === OrderPaidStatusEnum::Paid ? OrderStatusEnum::Paid : $this->order->status,
            ]);

            // 只有paypal 又没有地址的订单, 才是用户快速购买的
            // if ($this->payment instanceof PaypalOrder) {
            //     $this->order->loadMissing(['shippingAddress']);
            //     if (!$this->order->shippingAddress) {
            //         OrderSyncPaypalAddress::dispatch($this->order)->onQueue(QueueKey::Order->value);
            //     }
            // }
        } catch (Throwable $exception) {
            logger()->error('orderPaidError:' . $exception->getMessage());
        }
    }
}
