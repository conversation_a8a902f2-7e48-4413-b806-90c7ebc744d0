<?php

namespace App\Models\Product;

use App\Constants\QueueKey;
use App\Jobs\Product\ProductSyncErp;
use App\Jobs\Product\ProductSyncKeyword;
use App\Models\Attachment;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Collection;
use App\Models\CollectionProducts;
use App\Models\Comment;
use App\Models\Material;
use App\Models\Mode;
use App\Models\ModeProduct;
use App\Models\Style;
use App\Models\ActivityProduct;
use App\Models\User\User;
use App\Models\User\UserProductCollect;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use App\Services\ProductService;

/**
 * @property string $title
 * @property ?string $slug_title
 * @property ?string $spu
 * @property int $id
 * @property Carbon $make_at
 * @property Carbon $featured_at
 * @property Carbon $first_publish_at
 * @property bool $is_publish
 * @property bool $is_featured
 */
class Product extends MiddleModel
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    public $appends = [
        'sku',
        'is_activity'
    ];
    public $casts = [
        'make_at' => 'date',
        'first_publish_at' => 'datetime',
        'original_price' => 'float',
        'is_featured' => 'boolean',
        'is_new' => 'boolean',
        'origin_price' => 'decimal:2',
        'min_price' => 'decimal:2',
        'max_price' => 'decimal:2',
    ];

    public static function booted(): void
    {
        // 保存的时候执行
        static::saving(function (self $product) {
            $product->slug_title ??= $product->generateSlugTitle();
            if ($product->isDirty(['is_publish']) && $product->is_publish) {
                $product->first_publish_at ??= now();
                $product->new_publish_at = now();
            }
        });
        // 创建的时候执行
        static::created(function (self $product) {
            $product->loadMissing(['category', 'style', 'material', 'brand']);
            // 首次创建直接更新会导致进入循环
            $product->refresh()->update([
                'category_name' => $product->category->name,
                'style_name' => $product->style->name,
                'material_name' => $product->material->name,
                'brand_name' => $product->brand->name,
                'spu' => $product->spu ?: $product->generateSpu()
            ]);
            // 清理商品相关缓存
            app(ProductService::class)->clearProductCache($product);
        });
        // 如果spu发生变更，sku同步修改
        static::updated(function (self $product) {
            // 上架状态更新关键词
            if ($product->is_publish) {
                ProductSyncKeyword::dispatch($product)->onQueue(QueueKey::Product->value);
            }
            // // 修改上下架状态同步到erp
            // if ($product->isDirty(['is_publish'])) {
            //     ProductSyncErp::dispatch($product)->onQueue(QueueKey::Product->value);
            // }
            // 清理商品相关缓存
            app(ProductService::class)->clearProductCache($product);
        });
    }

    /**
     * 生成产品slug title
     * @return string
     */
    public function generateSlugTitle()
    {
        $slugs = [];
        // 关键词
        $keywords = explode(',', $this->keywords);
        foreach ($keywords as $keyword) {
            if (Str::slug($keyword)) {
                $slugs[] = Str::slug($keyword);
            }
        }
        // 标题
        if (Str::slug($this->title)) {
            $slugs[] = Str::slug($this->title);
        }

        return implode('-', $slugs);
    }

    public function sku(): Attribute
    {
        return Attribute::get(function () {
            return $this->spu;
        });
    }

    public function comments(): HasMany
    {
        return $this->hasMany(Comment::class);
    }

    public function commentStatic(): HasOne
    {
        return $this->hasOne(ProductCommentStatic::class);
    }


    public function generateSpu(): string
    {
        $this->loadMissing(['category', 'style', 'material', 'brand']);
        $diffYear = $this->make_at->diffInYears('2024-01-01');
        return implode('', [
            $this->brand->spu_key,
            $this->category->spu_key,
            $this->style->spu_key,
            $this->material->spu_key,
            chr(65 + $diffYear),
            Str::padLeft($this->make_at->month, 2, 0),
            Str::padLeft($this->id, 4, 0)
        ]);
    }

    /**
     * 变种信息
     * @return HasMany
     */
    public function variants(): HasMany
    {
        return $this->hasMany(ProductVariant::class);
    }

    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class)->withTrashed();
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class)->withTrashed();
    }

    public function material(): BelongsTo
    {
        return $this->belongsTo(Material::class)->withTrashed();
    }

    public function style(): BelongsTo
    {
        return $this->belongsTo(Style::class)->withTrashed();
    }

    public function image(): BelongsTo
    {
        return $this->belongsTo(Attachment::class, 'image_id');
    }

    public function video(): BelongsTo
    {
        return $this->belongsTo(Attachment::class, 'video_id');
    }


    /**
     * 关键词
     * @return HasOne
     */
    public function keyword(): HasOne
    {
        return $this->hasOne(ProductKeyword::class);
    }

    /**
     * 所属导航集合
     * @return BelongsToMany
     */
    public function collections(): BelongsToMany
    {
        return $this->belongsToMany(Collection::class, CollectionProducts::class);
    }

    /**
     * 所属导航集合id列表
     * @return HasMany
     */
    public function collectionIds(): HasMany
    {
        return $this->hasMany(CollectionProducts::class);
    }

    /**
     * 收藏商品的用户
     * @return BelongsToMany
     */
    public function collectUsers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, UserProductCollect::class);
    }

    /**
     * 收藏商品的用户ids (提供sql查询便携)
     * @return HasMany
     */
    public function collectUserIds(): HasMany
    {
        return $this->hasMany(UserProductCollect::class);
    }

    /**
     * 用户体验活动
     * @return HasMany
     */
    public function activity_products(): HasMany
    {
        return $this->hasMany(ActivityProduct::class);
    }

    /**
     * 商品风格
     * @return BelongsToMany
     */
    public function modes(): BelongsToMany
    {
        return $this->belongsToMany(Mode::class, ModeProduct::class);
    }

    /**
     * 商品风格id列表
     * @return HasMany
     */
    public function modeIds(): HasMany
    {
        return $this->hasMany(ModeProduct::class);
    }

    /**
     * 商品颜色对应图片
     * @return HasMany
     */
    public function colors(): HasMany
    {
        return $this->hasMany(ProductColor::class);
    }

    public function getIsActivityAttribute()
    {
        return $this->attributes['is_activity'] ?? false;
    }

    /**
     * 获取该商品的推荐商品关系
     */
    public function productRecommends()
    {
        return $this->hasMany(ProductRecommend::class, 'product_id');
    }

}
