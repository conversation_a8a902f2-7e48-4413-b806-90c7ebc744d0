# 数量折扣系统说明

## 概述

新的数量折扣系统支持两种折扣模式：

1. **区间折扣**：根据购买数量的区间给予不同折扣
2. **循环折扣**：按照固定的循环模式重复应用折扣

## 1. 区间折扣 (IntervalDiscount)

### 示例：第1-3件9折，第4件以上8折

```json
{
  "discount_mode": 1,
  "rules": [
    {
      "quantity": 1,
      "quantity_end": 3,
      "discount_amount_type": 2,
      "discount_rate": 10,
      "discount_max_price": 100
    },
    {
      "quantity": 4,
      "quantity_end": 999,
      "discount_amount_type": 2,
      "discount_rate": 20,
      "discount_max_price": 200
    }
  ]
}
```

### 购买场景示例：
- 购买5件商品（每件100元）
- 第1-3件：享受9折（每件90元）
- 第4-5件：享受8折（每件80元）
- 总价：90×3 + 80×2 = 430元

## 2. 循环折扣 (CyclicDiscount)

### 示例：循环件数3件，第1件不打折，第2件9折，第3件8折

```json
{
  "discount_mode": 2,
  "cycle_quantity": 3,
  "rules": [
    {
      "quantity": 1,
      "discount_amount_type": 2,
      "discount_rate": 0,
      "discount_max_price": 0
    },
    {
      "quantity": 2,
      "discount_amount_type": 2,
      "discount_rate": 10,
      "discount_max_price": 50
    },
    {
      "quantity": 3,
      "discount_amount_type": 2,
      "discount_rate": 20,
      "discount_max_price": 100
    }
  ]
}
```

### 购买场景示例：
- 购买7件商品（每件100元）
- 第1件：原价（100元）
- 第2件：9折（90元）
- 第3件：8折（80元）
- 第4件：原价（100元）- 循环重新开始
- 第5件：9折（90元）
- 第6件：8折（80元）
- 第7件：原价（100元）- 循环重新开始
- 总价：100 + 90 + 80 + 100 + 90 + 80 + 100 = 640元

## 数据结构说明

### 字段说明：
- `discount_mode`: 折扣模式（1=区间折扣，2=循环折扣）
- `cycle_quantity`: 循环件数（仅循环折扣需要）
- `rules`: 折扣规则数组
  - `quantity`: 第几件或区间起始件数
  - `quantity_end`: 区间结束件数（仅区间折扣需要）
  - `discount_amount_type`: 折扣类型（1=固定金额，2=百分比）
  - `discount_rate`: 折扣率（百分比）
  - `discount_price`: 固定折扣金额
  - `discount_max_price`: 最大折扣金额

### 折扣类型：
1. **固定金额** (FixedAmount): 直接减去固定金额
2. **百分比** (PercentAmount): 按百分比计算折扣
3. **随机金额** (RandomAmount): 在指定范围内随机折扣

## 实现逻辑

### 区间折扣逻辑：
1. 按商品价格降序排列
2. 逐件处理，根据当前件数找到对应的区间规则
3. 应用相应的折扣

### 循环折扣逻辑：
1. 按商品价格降序排列
2. 逐件处理，根据 `(当前件数-1) % cycle_quantity + 1` 计算循环位置
3. 应用循环内对应位置的折扣规则

## 注意事项

1. 商品按价格降序处理，确保高价商品优先享受折扣
2. 每件商品会被拆分为单独的购物车项，便于精确计算折扣
3. 原有的购物车项数量会被设为0并在后续删除
4. 新的折扣系统向后兼容，如果没有指定 `discount_mode`，默认使用区间折扣模式
