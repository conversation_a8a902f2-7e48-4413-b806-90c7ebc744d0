<?php

namespace App\Models\Enums\Discount;

use Illuminate\Support\Arr;

enum QuantityDiscountModeEnum: int
{
    case IntervalDiscount = 1;  // 区间折扣
    case CyclicDiscount = 2;    // 循环折扣

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::IntervalDiscount->value => '区间折扣',
            self::CyclicDiscount->value => '循环折扣',
        ];
    }
}
