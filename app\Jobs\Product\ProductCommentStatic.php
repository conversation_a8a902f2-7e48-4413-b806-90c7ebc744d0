<?php

namespace App\Jobs\Product;

use App\Models\Comment;
use App\Models\Enums\Comment\CommentStatusEnum;
use App\Models\Product\ProductCommentStatic as CommentStaticModel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;

class ProductCommentStatic implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $productIds;

    /**
     * 更新商品评论静态数据
     */
    public function __construct(int|array $productIds)
    {
        $this->productIds = Arr::wrap($productIds);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        // 更新商品分数 (后期量大考虑批量处理,暂时先循环)
        foreach ($this->productIds as $productId) {
            $productCommentStatic = CommentStaticModel::query()->firstOrCreate(['product_id' => $productId]);
            $baseQuery = Comment::query()
                ->where('status', CommentStatusEnum::Approved)
                ->where('product_id', $productId);
            $productCommentStatic->fill([
                'avg_grade' => round($baseQuery->avg('grade')),
                'number' => $baseQuery->count()
            ])->save();
        }
    }
}
