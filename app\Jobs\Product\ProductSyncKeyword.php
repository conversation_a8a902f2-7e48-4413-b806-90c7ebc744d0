<?php

namespace App\Jobs\Product;

use App\Models\Product\Product;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

/**
 * 处理关键词，提供模糊搜索
 */
class ProductSyncKeyword implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public Product $product)
    {
    }


    public function handle(): void
    {
        // 发布的才需要处理
        if ($this->product->is_publish) {
            $variants = $this->product->variants()
                ->select(['id', 'sku', 'color_attribute_value_id', 'size_attribute_value_id'])
                ->with(['colorAttribute:id,value', 'sizeAttribute:id,value'])
                ->get();
            $colors = $sizes = $skus = [];
            foreach ($variants as $variant) {
                $skus[] = $variant->sku;
                $colors[] = 'color' . $variant->colorAttribute->value;
                $sizes[] = 'size' . $variant->sizeAttribute->value;
            }
            $collectionNames = $this->product->collections()->pluck('title')->join(' ');

            $this->product->keyword()->updateOrCreate([], [
                'title' => strtolower($this->product->title),
                'slug_title' => strtolower(str_replace('-', ' ', $this->product->slug_title)),
                'category_name' => strtolower($this->product->category_name),
                'material_name' => strtolower($this->product->material_name),
                'sku' => strtolower(implode(' ', array_unique($skus))),
                'sizes' => strtolower(implode(' ', array_unique($sizes))),
                'colors' => strtolower(implode(' ', array_unique($colors))),
                'collection_names' => strtolower($collectionNames),
            ]);
        }
    }
}
