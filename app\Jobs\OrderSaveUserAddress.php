<?php

namespace App\Jobs;

use App\Models\Order\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;

class OrderSaveUserAddress implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 订单地址保存到用户
     */
    public function __construct(public Order $order)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $order = $this->order;
        $user = $this->order->user;
        if (!$user) {
            return;
        }
        $order->loadMissing(['billingAddress', 'shippingAddress']);
        if ($order->billingAddress->is_save) {
            $base = $order->billingAddress->toArray();
            $user->addresses()->create([
                ...Arr::only($base, ['first_name', 'last_name', 'country_id', 'address', 'address1', 'state', 'city', 'zip','phone']),
                'is_billing_default' => true,
            ]);
        }
        if ($order->shippingAddress->is_save) {
            $base = $order->shippingAddress->toArray();
            $user->addresses()->create([
                ...Arr::only($base, ['first_name', 'last_name', 'country_id', 'address', 'address1', 'state', 'city', 'zip','phone']),
                'is_shipping_default' => true
            ]);
        }
        //
    }
}
