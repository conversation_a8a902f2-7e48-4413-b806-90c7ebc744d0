<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 修复 cart_items 表中 add_price 为 0 的记录
        // 将 add_price 设置为对应的 price 值
        DB::statement("
            UPDATE cart_items 
            SET add_price = price 
            WHERE add_price = 0 OR add_price IS NULL
        ");
        
        // 对于活动商品，如果 add_price 仍然为 0，则从 product_variants 表获取价格
        DB::statement("
            UPDATE cart_items ci
            JOIN product_variants pv ON ci.product_variant_id = pv.id
            SET ci.add_price = pv.price
            WHERE (ci.add_price = 0 OR ci.add_price IS NULL) 
            AND ci.is_activity = 1
        ");
        
        // 对于非活动商品，确保 add_price 不为空
        DB::statement("
            UPDATE cart_items ci
            JOIN product_variants pv ON ci.product_variant_id = pv.id
            SET ci.add_price = COALESCE(ci.price, pv.price)
            WHERE (ci.add_price = 0 OR ci.add_price IS NULL)
            AND ci.is_activity = 0
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 不需要回滚，因为这是数据修复
    }
};
