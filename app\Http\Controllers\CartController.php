<?php

namespace App\Http\Controllers;


use App\Constants\ErrorCode;
use App\Exceptions\DataException;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Coupon\Coupon;
use App\Models\Enums\Discount\DiscountTypeEnum;
use App\Models\Shipping;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use App\Models\User\User;
use App\Models\User\UserAddress;
use Throwable;


class CartController extends Controller
{
    public function show(Request $request): JsonResource
    {
        $validated = $request->validate(['session_uuid' => 'nullable|uuid', 'country' => 'nullable|string']);
        /**
         * @var $user User|null
         */
        $user = Auth::user();
        $session_uuid = Arr::get($validated, 'session_uuid');
        /**
         * @var Cart $cart
         */
        $cart = userService()->getOrCreateCart($user, $session_uuid);
        $cart->load([
            'items',
            'items.product:id,spu,title,slug_title',
            'items.product.commentStatic',
            'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id',
            'items.productVariant.image:id,path,disk,module',
            'items.productVariant.sizeAttribute:id,value',
            'items.productVariant.colorAttribute:id,value',
            'items.coupon',
        ]);

        // 刷新购物车数据
        if (cartService()->refreshCartInfo($cart)) {
            cartService()->refreshCart($cart);
        }
        
        $shippings = cartService()->getShippingFee($cart);
        $cart->shippings = $shippings;
        // 运费实际金额
        return JsonResource::make($cart);
    }

    /**
     * 删除购物车产品
     * @param Request $request
     * @param CartItem $item
     * @return JsonResource
     * @throws DataException
     */
    public function itemDestroy(Request $request, CartItem $item): JsonResource
    {
        $validated = $request->validate([
            'session_uuid' => 'uuid',
        ]);
        /** @var User|null $user */
        $user = Auth::user();
        $session_uuid = Arr::get($validated, 'session_uuid');
        /** @var Cart $cart  */
        $cart = userService()->getOrCreateCart($user, $session_uuid);
        // 非本人 或非此购物车的数据
        if (!$cart->items()->where('id', $item->id)->exists()) {
            throw new DataException("The requested data does not exist.", ErrorCode::HttpNotFound);
        }
        $item->delete();

        // 更新总订单数据
        $cart = cartService()->refreshCart($cart, null, true);
        $cart->load([
            'items',
            'items.product:id,spu,title',
            'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id',
            'items.productVariant.image:id,path,disk,module',
            'items.productVariant.sizeAttribute:id,value',
            'items.productVariant.colorAttribute:id,value',
            'items.coupon',
        ]);
        return JsonResource::make($cart);
    }


    /**
     * 修改购物车商品数量
     * @param Request $request
     * @param CartItem $item
     * @return JsonResource
     * @throws DataException
     */
    public function itemUpdate(Request $request, CartItem $item): JsonResource
    {
        $validated = $request->validate([
            'num' => 'numeric|min:1',
            'session_uuid' => 'uuid',
        ]);
        /** @var User|null $user */
        $user = Auth::user();
        $session_uuid = Arr::get($validated, 'session_uuid');
        /** @var Cart $cart  */
        $cart = userService()->getOrCreateCart($user, $session_uuid);
        // 非本人 或非此购物车的数据
        if (!$cart->items()->where('id', $item->id)->exists()) {
            throw new DataException("The requested data does not exist.", ErrorCode::HttpNotFound);
        }
        // 更新购物车数量
        $cart = cartService()->updateCartItem($cart, $item, $validated['num']);
        // 刷新购物车数据
        $cart = cartService()->refreshCart($cart, null, true);
        $cart->load([
            'items',
            'items.product:id,spu,title',
            'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id',
            'items.productVariant.image:id,path,disk,module',
            'items.productVariant.sizeAttribute:id,value',
            'items.productVariant.colorAttribute:id,value',
            'items.coupon',
        ]);
        return JsonResource::make($cart);
    }

    /**
     * 合并购物车
     * @param Request $request
     * @return JsonResource
     * @throws Throwable
     */
    public function mergeCart(Request $request): JsonResource
    {
        $validated = $request->validate([
            'session_uuid' => ['required', 'uuid', new Exists(Cart::class, 'session_uuid')],
        ]);
        /** @var User|null $user */
        $user = Auth::user();
        /** @var Cart $userCart  */
        $userCart = userService()->getOrCreateCart($user);
        $sessionCart = userService()->getOrCreateCart(null, Arr::get($validated, 'session_uuid'));
        DB::beginTransaction();
        try {
            $items = $sessionCart->items()->with('productVariant')->get();
            // 获取用户的购物车特价商品
            $userCartItems = $userCart->items()->where('is_activity', 1)->get();
            // 获取session购物车中的特价商品
            $sessionCartItems = $sessionCart->items()->where('is_activity', 1)->get();
            // 如果都有保留最早添加到购物车的特价产品另外的还原价格
            if ($userCartItems->isNotEmpty() && $sessionCartItems->isNotEmpty()) {
                $allActivityItems = $userCartItems->merge($sessionCartItems);
                // 找到创建时间最早的那个
                $earliestCartItem = $allActivityItems->sortBy('created_at')->first();
                $preservedCartItemId = $earliestCartItem->id;
            }
            foreach ($items as $item) {
                $variant = $item->productVariant;
                $oldItem = $userCart->items()->where('product_variant_id', $item['product_variant_id'])->first();
                if ($oldItem) {
                    // 如果 oldItem 是 preservedCartItemId，跳过，新增一个新的原价
                    if (!empty($preservedCartItemId) && $oldItem->id === $preservedCartItemId) {
                        // 直接新增商品
                        $newItem = $userCart->items()->create([
                            ...$item->only(['product_id', 'product_variant_id']),
                            'price' => $variant->price,
                            'original_price' => $variant->original_price,
                        ]);
                        continue;
                    }

                    $updateData = [
                        'num' => DB::raw('num + ' . $item->num),
                        'price' => $variant->price,
                        'original_price' => $variant->original_price,
                    ];

                    // 如果 oldItem 是特价商品，但不是 preservedCartItemId，则重置活动相关字段
                    if ($oldItem->is_activity) {
                        $updateData['user_experience_activity_id'] = null;
                        $updateData['is_activity'] = 0;
                    }

                    $oldItem->update($updateData);
                } else {
                    // 直接新增商品
                    $newItem = $userCart->items()->create([
                        ...$item->only(['product_id', 'product_variant_id', 'user_experience_activity_id', 'is_activity', 'price']),
                        // 'price' => $variant->price,
                        'original_price' => $variant->original_price,
                    ]);

                    // 如果新加入的是特价商品，且不是 preservedCartItemId，则重置活动相关字段
                    if ($newItem->is_activity && !empty($preservedCartItemId) && $newItem->id !== $preservedCartItemId) {
                        $newItem->update([
                            'user_experience_activity_id' => null,
                            'is_activity' => 0,
                            'price' => $variant->price,
                        ]);
                    }
                }
            }
            // 删除历史item数据
            $sessionCart->delete();
            $sessionCart->items()->delete();

            // 刷新购物车数据
            $userCart = cartService()->refreshCart($userCart);
            $userCart->load([
                'items',
                'items.product:id,spu,title',
                'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id',
                'items.productVariant.image:id,path,disk,module',
                'items.productVariant.sizeAttribute:id,value',
                'items.productVariant.colorAttribute:id,value',
                'items.coupon',
            ]);
            DB::commit();
        } catch (Throwable $exception) {
            DB::rollBack();
        }

        return JsonResource::make($userCart);
    }

    /**
     * 更新购物车运输方式
     * @param Request $request
     * @return JsonResource
     */
    public function updateCart(Request $request): JsonResource
    {
        $validated = $request->validate([
            'session_uuid' => 'nullable|uuid',
            'shipping_id' => ['int', new Exists(Shipping::class, 'id')],
        ]);
        /** @var User|null $user */
        $user = Auth::user();
        $session_uuid = Arr::get($validated, 'session_uuid');
        /** @var Cart $cart  */
        $cart = userService()->getOrCreateCart($user, $session_uuid);
        // 更新购物车运费
        $shipping = Shipping::query()->find(Arr::get($validated, 'shipping_id'));
        $cart->update([
            'shipping_id' => $shipping->id,
            'shipping_type' => $shipping->type,
        ]);
        // 刷新购物车
        $cart = cartService()->refreshCart($cart, null, true);
        $cart->load([
            'items',
            'items.product:id,spu,title',
            'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id',
            'items.productVariant.image:id,path,disk,module',
            'items.productVariant.sizeAttribute:id,value',
            'items.productVariant.colorAttribute:id,value',
            'items.coupon',
        ]);
        return JsonResource::make($cart);
    }

    /**
     * 添加购物车
     * @param Request $request
     * @return JsonResource
     */
    public function addCart(Request $request): JsonResource
    {
        $validated = $request->validate([
            'session_uuid' => 'nullable|uuid',
            'items' => 'array|required',
            'items.*.product_id' => ['required', 'int', 'exists:products,id'],
            'items.*.product_variant_id' => ['required', 'int', 'exists:product_variants,id'],
            'items.*.num' => 'numeric|required|min:1',
            'from_activity' => 'nullable|bool',
        ]);
        /** @var User|null $user */
        $user = Auth::user();
        $session_uuid = Arr::get($validated, 'session_uuid');
        /** @var Cart $cart  */
        $cart = userService()->getOrCreateCart($user, $session_uuid);
        //是否活动页
        $from_activity = Arr::get($validated, 'from_activity', false);
        // 获取购物车内容
        $items = Arr::get($validated, 'items', []);
        // 添加购物车
        foreach ($items as $item) {
            cartService()->addCartItem($cart, $item, $user, $session_uuid, $from_activity);
        }
        // 刷新购物车
        $cart = cartService()->refreshCart($cart, null, true);
        $cart->load([
            'items',
            'items.product:id,spu,title',
            'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id',
            'items.productVariant.image:id,path,disk,module',
            'items.productVariant.sizeAttribute:id,value',
            'items.productVariant.colorAttribute:id,value',
            'items.coupon',
        ]);
        return JsonResource::make($cart);
    }


    /**
     * 绑定优惠券 --- 现确认以后都没有运费优惠券 --- 确认人：许家旺 2025/05/23
     * @param Request $request
     * @return JsonResource
     * @throws DataException
     */
    public function selectCoupon(Request $request): JsonResource
    {
        $validated = $request->validate([
            'session_uuid' => 'nullable|uuid',
            'code' => 'required|string',
        ]);
        /**
         * @var $user User|null
         */
        $user = Auth::user();
        $session_uuid = Arr::get($validated, 'session_uuid');
        /**
         * @var Cart $cart
         */
        $cart = userService()->getOrCreateCart($user, $session_uuid);
        $code = Arr::get($validated, 'code');
        $coupon = null;

        // 客户只能使用非自动使用的优惠券
        $coupon = Coupon::query()->where('code', $code)->where('is_auto', 0)->first();
        if (!$coupon) {
            throw new DataException("Coupon failed.");
        }

        // 判断优惠券使用类型， 两者都需要判断该用户是否还有可用库存
        if ($coupon->is_global) {
            // 全局优惠券
            if ($user) {
                //登录用户需要判断是否使用过
                $userCoupon = $user->userCoupons()->where('code', $code)->whereNotNull('used_at')->first();
                if ($userCoupon) {
                    throw new DataException("Coupon failed.");
                }
            }
            // 判断优惠券是否过期
            if (now()->lt($coupon->effective_start_at)) {
                throw new DataException('This coupon is not yet available.');
            }
            if (now()->gt($coupon->effective_end_at)) {
                throw new DataException('This coupon has expired.');
            };
        } else {
            // 个人优惠券
            $userCoupon = $user->userCoupons()->where('code', $code)->whereNull('used_at')->first();
            if (!$userCoupon) {
                throw new DataException("Coupon failed.");
            }
            if (now()->lt($userCoupon->effective_start_at)) {
                throw new DataException('This coupon has expired.');
            };
            if (now()->gt($userCoupon->effective_end_at)) {
                throw new DataException('This coupon has expired.');
            };
        }

        try {
            DB::beginTransaction();
            // 绑定新的优惠券
            $cart = cartService()->refreshCart($cart, $coupon);
            DB::commit();
        } catch (DataException $e) {
            DB::rollBack();
            throw $e;
        } catch (Throwable $exception) {
            DB::rollBack();
            throw new DataException('Coupon Use Error.');
        }

        $cart->refresh();
        // 返回购物车信息
        $cart->load([
            'items',
            'items.product:id,spu,title',
            'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id',
            'items.productVariant.image:id,path,disk,module',
            'items.productVariant.sizeAttribute:id,value',
            'items.productVariant.colorAttribute:id,value',
            'items.coupon',
        ]);
        // 折扣率
        return JsonResource::make($cart);
    }

    /**
     * 获取运费优惠
     * @param \Illuminate\Http\Request $request
     * @return JsonResource
     */
    public function shippingCoupon(Request $request)
    {
        // 获取所有满足条件的优惠券
        $coupon_rules = Coupon::query()
            ->where('type', DiscountTypeEnum::FreeShippingPromotion->value)
            ->where('is_global', true)
            ->where('enabled', true)
            ->where('effective_start_at', '<=', now())
            ->where('effective_end_at', '>=', now())
            ->whereNull('total_count')
            ->whereNull('user_count')
            ->pluck('rules');
        // 规则
        $rules = [];
        if ($coupon_rules->isNotEmpty()) {
            foreach ($coupon_rules as $coupon_rule) {
                foreach ($coupon_rule as $rule) {
                    $rules[Arr::get($rule, 'shipping_id')] = [
                        'price' => Arr::get($rule, 'price'),
                        'price_agent' => convertPrice(Arr::get($rule, 'price'), currentCurrency()),
                        'shipping' => Shipping::query()->where('id', Arr::get($rule, 'shipping_id'))->first(),
                    ];
                }
            }
        }
        return JsonResource::make($rules);
    }

    /**
     * 更新购物车
     * @param Request $request
     * @return JsonResource
     */
    public function removeCoupon(Request $request): JsonResource
    {
        $validated = $request->validate([
            'session_uuid' => 'nullable|uuid',
        ]);
        /** @var User|null $user */
        $user = Auth::user();
        $session_uuid = Arr::get($validated, 'session_uuid');
        /** @var Cart $cart  */
        $cart = userService()->getOrCreateCart($user, $session_uuid);
        // 刷新购物车
        cartService()->refreshCart($cart, null, true);
        $cart->load([
            'items',
            'items.product:id,spu,title',
            'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id',
            'items.productVariant.image:id,path,disk,module',
            'items.productVariant.sizeAttribute:id,value',
            'items.productVariant.colorAttribute:id,value',
            'items.coupon',
        ]);
        return JsonResource::make($cart);
    }

    /**
     * 切换收货地址
     * @param Request $request
     * @return JsonResource
     */
    public function switchCountry(Request $request): JsonResource
    {
        $validated = $request->validate([
            'session_uuid' => 'nullable|uuid',
            'country' => ['required', 'string'],
        ]);
        /** @var User|null $user */
        $user = Auth::user();
        $session_uuid = Arr::get($validated, 'session_uuid');
        $cart = userService()->getOrCreateCart($user, $session_uuid);
        $country = Arr::get($validated, 'country');
        Cart::where('id', $cart->id)->update([
            'country' => $country,
            'country_updated_at' => now(),
        ]);
        $cart->refresh();
        $cart->load([
            'items',
            'items.product:id,spu,title',
            'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id',
            'items.productVariant.image:id,path,disk,module',
            'items.productVariant.sizeAttribute:id,value',
            'items.productVariant.colorAttribute:id,value',
            'items.coupon',
        ]);
        return JsonResource::make($cart);
    }
}
